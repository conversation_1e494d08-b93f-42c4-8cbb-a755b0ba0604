import { expect, use } from "chai";
import * as sinon from "sinon";
import { getEntityStaticDomainService, validateDomainTypeMatch } from "../../skywind/services/entityStaticDomainService";
import { getStaticDomainService } from "../../skywind/services/domain";
import { StaticDomainType } from "../../skywind/entities/domain";
import { BaseEntity } from "../../skywind/entities/entity";
import * as Errors from "../../skywind/errors";
import { factory } from "factory-girl";
import { FACTORY } from "../factories/common";
import { truncate } from "../entities/helper";

use(require("chai-as-promised"));

describe("EntityStaticDomainService - Domain Type Validation", () => {
    let sandbox: sinon.SinonSandbox;
    let entity: BaseEntity;
    let staticDomainService: any;
    let entityStaticDomainService: any;

    before(async () => {
        await truncate();
    });

    beforeEach(() => {
        sandbox = sinon.createSandbox();
        staticDomainService = getStaticDomainService();
        entityStaticDomainService = getEntityStaticDomainService();
    });

    afterEach(() => {
        sandbox.restore();
    });

    describe("validateDomainTypeMatch", () => {
        it("should pass validation when domain type matches expected type", () => {
            const domain = {
                id: 1,
                domain: "test.lobby.com",
                type: StaticDomainType.LOBBY,
                status: "active"
            } as any;

            expect(() => validateDomainTypeMatch(domain, StaticDomainType.LOBBY)).to.not.throw();
        });

        it("should throw ValidationError when domain type does not match expected type", () => {
            const domain = {
                id: 1,
                domain: "test.lobby.com",
                type: StaticDomainType.LOBBY,
                status: "active"
            } as any;

            expect(() => validateDomainTypeMatch(domain, StaticDomainType.EHUB))
                .to.throw(Errors.ValidationError)
                .with.property("message")
                .that.include("Domain type mismatch: Cannot assign lobby domain to ehub domain slot");
        });

        it("should include domain details in error message", () => {
            const domain = {
                id: 1,
                domain: "test.lobby.com",
                type: StaticDomainType.LOBBY,
                status: "active"
            } as any;

            expect(() => validateDomainTypeMatch(domain, StaticDomainType.LIVE_STREAMING))
                .to.throw(Errors.ValidationError)
                .with.property("message")
                .that.include("Domain 'test.lobby.com' has type 'lobby' but expected type 'live-streaming'");
        });

        it("should validate all domain types correctly", () => {
            const testCases = [
                { domainType: StaticDomainType.STATIC, expectedType: StaticDomainType.STATIC, shouldPass: true },
                { domainType: StaticDomainType.LOBBY, expectedType: StaticDomainType.LOBBY, shouldPass: true },
                { domainType: StaticDomainType.EHUB, expectedType: StaticDomainType.EHUB, shouldPass: true },
                { domainType: StaticDomainType.LIVE_STREAMING, expectedType: StaticDomainType.LIVE_STREAMING, shouldPass: true },
                { domainType: StaticDomainType.STATIC, expectedType: StaticDomainType.LOBBY, shouldPass: false },
                { domainType: StaticDomainType.LOBBY, expectedType: StaticDomainType.EHUB, shouldPass: false },
                { domainType: StaticDomainType.EHUB, expectedType: StaticDomainType.LIVE_STREAMING, shouldPass: false },
                { domainType: StaticDomainType.LIVE_STREAMING, expectedType: StaticDomainType.STATIC, shouldPass: false }
            ];

            testCases.forEach(({ domainType, expectedType, shouldPass }) => {
                const domain = {
                    id: 1,
                    domain: `test.${domainType}.com`,
                    type: domainType,
                    status: "active"
                } as any;

                if (shouldPass) {
                    expect(() => validateDomainTypeMatch(domain, expectedType), 
                        `Should pass: ${domainType} -> ${expectedType}`).to.not.throw();
                } else {
                    expect(() => validateDomainTypeMatch(domain, expectedType), 
                        `Should fail: ${domainType} -> ${expectedType}`).to.throw(Errors.ValidationError);
                }
            });
        });
    });

    describe("EntityStaticDomainService.set with domain type validation", () => {
        let lobbyDomain: any;
        let ehubDomain: any;
        let liveStreamingDomain: any;
        let staticDomain: any;

        beforeEach(async () => {
            entity = await factory.create(FACTORY.BRAND);
            
            // Create domains of different types
            lobbyDomain = await staticDomainService.create({
                domain: "test.lobby.com",
                type: StaticDomainType.LOBBY
            });
            
            ehubDomain = await staticDomainService.create({
                domain: "test.ehub.com", 
                type: StaticDomainType.EHUB
            });
            
            liveStreamingDomain = await staticDomainService.create({
                domain: "test.live.com",
                type: StaticDomainType.LIVE_STREAMING
            });
            
            staticDomain = await staticDomainService.create({
                domain: "test.static.com",
                type: StaticDomainType.STATIC
            });
        });

        it("should successfully assign lobby domain to lobby slot", async () => {
            const result = await entityStaticDomainService.set(entity, lobbyDomain.id, StaticDomainType.LOBBY);
            expect(result).to.deep.equal(lobbyDomain);
            expect(entity.lobbyDomainId).to.equal(lobbyDomain.id);
        });

        it("should successfully assign ehub domain to ehub slot", async () => {
            const result = await entityStaticDomainService.set(entity, ehubDomain.id, StaticDomainType.EHUB);
            expect(result).to.deep.equal(ehubDomain);
            expect(entity.ehubDomainId).to.equal(ehubDomain.id);
        });

        it("should successfully assign live streaming domain to live streaming slot", async () => {
            const result = await entityStaticDomainService.set(entity, liveStreamingDomain.id, StaticDomainType.LIVE_STREAMING);
            expect(result).to.deep.equal(liveStreamingDomain);
            expect(entity.liveStreamingDomainId).to.equal(liveStreamingDomain.id);
        });

        it("should successfully assign static domain to static slot", async () => {
            const result = await entityStaticDomainService.set(entity, staticDomain.id, StaticDomainType.STATIC);
            expect(result).to.deep.equal(staticDomain);
            expect(entity.staticDomainId).to.equal(staticDomain.id);
        });

        it("should reject assigning lobby domain to ehub slot", async () => {
            await expect(entityStaticDomainService.set(entity, lobbyDomain.id, StaticDomainType.EHUB))
                .to.be.rejectedWith(Errors.ValidationError)
                .and.eventually.have.property("message")
                .that.include("Domain type mismatch: Cannot assign lobby domain to ehub domain slot");
        });

        it("should reject assigning ehub domain to lobby slot", async () => {
            await expect(entityStaticDomainService.set(entity, ehubDomain.id, StaticDomainType.LOBBY))
                .to.be.rejectedWith(Errors.ValidationError)
                .and.eventually.have.property("message")
                .that.include("Domain type mismatch: Cannot assign ehub domain to lobby domain slot");
        });

        it("should reject assigning static domain to live streaming slot", async () => {
            await expect(entityStaticDomainService.set(entity, staticDomain.id, StaticDomainType.LIVE_STREAMING))
                .to.be.rejectedWith(Errors.ValidationError)
                .and.eventually.have.property("message")
                .that.include("Domain type mismatch: Cannot assign static domain to live-streaming domain slot");
        });

        it("should reject assigning live streaming domain to static slot", async () => {
            await expect(entityStaticDomainService.set(entity, liveStreamingDomain.id, StaticDomainType.STATIC))
                .to.be.rejectedWith(Errors.ValidationError)
                .and.eventually.have.property("message")
                .that.include("Domain type mismatch: Cannot assign live-streaming domain to static domain slot");
        });

        it("should not modify entity when domain type validation fails", async () => {
            const originalLobbyDomainId = entity.lobbyDomainId;
            const originalEhubDomainId = entity.ehubDomainId;
            
            try {
                await entityStaticDomainService.set(entity, lobbyDomain.id, StaticDomainType.EHUB);
            } catch (error) {
                // Expected to fail
            }
            
            // Entity should not be modified
            expect(entity.lobbyDomainId).to.equal(originalLobbyDomainId);
            expect(entity.ehubDomainId).to.equal(originalEhubDomainId);
        });
    });
});
