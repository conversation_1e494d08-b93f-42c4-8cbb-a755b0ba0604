import EntityCache from "../cache/entity";
import { StaticDomain, StaticDomainType } from "../entities/domain";
import { BaseEntity, ChildEntity, EntityInfo } from "../entities/entity";
import * as Errors from "../errors";
import { Models } from "../models/models";
import { StaticDomainService, getStaticDomainService } from "./domain";
import { EntityStaticDomainPoolService } from "./entityStaticDomainPool";
import { default as EntitySettingsService } from "./settings";

export const getEntityStaticDomainService = () => new EntityStaticDomainService(getStaticDomainService());

class EntityStaticDomainService {
    constructor(private domainService: StaticDomainService) {
    }

    public async set(entity: BaseEntity, domainId: number, domainType = StaticDomainType.STATIC): Promise<StaticDomain> {
        const domain = await this.domainService.findOne(domainId);

        // Validate that the domain type matches the requested assignment type
        validateDomainTypeMatch(domain, domainType);

        const updateData: any = {};
        switch (domainType) {
            case StaticDomainType.STATIC:
                await validateStaticDomain(entity, domain);
                entity.staticDomainId = domain.id;
                updateData.staticDomainId = domain.id;
                break;
            case StaticDomainType.LOBBY:
                entity.lobbyDomainId = domain.id;
                updateData.lobbyDomainId = domain.id;
                break;
            case StaticDomainType.LIVE_STREAMING:
                entity.liveStreamingDomainId = domain.id;
                updateData.liveStreamingDomainId = domain.id;
                break;
            case StaticDomainType.EHUB:
                entity.ehubDomainId = domain.id;
                updateData.ehubDomainId = domain.id;
                break;
            default:
                throw new Errors.ValidationError(`Invalid domain type: ${domainType}`);
        }

        await Models.EntityModel.update(updateData, { where: { id: entity.id } });

        EntityCache.reset();

        return domain;
    }

    public async reset(entity: BaseEntity, domainType = StaticDomainType.STATIC): Promise<StaticDomain> {
        const updateData: any = {};
        switch (domainType) {
            case StaticDomainType.STATIC:
                entity.staticDomainId = undefined;
                updateData.staticDomainId = null;
                break;
            case StaticDomainType.LOBBY:
                entity.lobbyDomainId = undefined;
                updateData.lobbyDomainId = null;
                break;
            case StaticDomainType.LIVE_STREAMING:
                entity.liveStreamingDomainId = undefined;
                updateData.liveStreamingDomainId = null;
                break;
            case StaticDomainType.EHUB:
                entity.ehubDomainId = undefined;
                updateData.ehubDomainId = null;
                break;
            default:
                throw new Errors.ValidationError(`Invalid domain type: ${domainType}`);
        }

        await Models.EntityModel.update(updateData, { where: { id: entity.id } });

        EntityCache.reset();

        return this.get(entity, domainType);
    }

    public async pick(entity: BaseEntity, domainType = StaticDomainType.STATIC): Promise<StaticDomain> {
        let domain = await new EntityStaticDomainPoolService(entity).pickStaticDomain(domainType);
        if (!domain) {
            domain = await this.get(entity, domainType);
        }
        return domain;
    }

    public async get(entity: BaseEntity, domainType = StaticDomainType.STATIC): Promise<StaticDomain> {
        let domainId: number;
        switch (domainType) {
            case StaticDomainType.STATIC:
                domainId = entity.inheritedStaticDomainId;
                break;
            case StaticDomainType.LOBBY:
                domainId = entity.inheritedLobbyDomainId;
                break;
            case StaticDomainType.LIVE_STREAMING:
                domainId = entity.inheritedLiveStreamingDomainId;
                break;
            case StaticDomainType.EHUB:
                domainId = entity.inheritedEhubDomainId;
                break;
            default:
                throw new Errors.ValidationError(`Invalid domain type: ${domainType}`);
        }
        if (domainId) {
            return this.domainService.findOne(domainId);
        }
    }

    public async setTags(entity: BaseEntity, tags: string[]): Promise<EntityInfo> {
        entity.staticDomainTags = tags;
        await entity.save().finally(() => EntityCache.reset());
        return entity.toInfo();
    }

    public async resetTags(entity: BaseEntity): Promise<EntityInfo> {
        entity.staticDomainTags = null;
        await entity.save().finally(() => EntityCache.reset());
        return entity.toInfo();
    }
}

export async function validateStaticDomain(entity: BaseEntity, domain: StaticDomain) {
    if (!entity.validateStaticDomain(domain.domain)) {
        throw new Errors.ValidationError(`Domain is not valid. Allowed tags - ${entity.staticDomainTags}`);
    }
    if (!entity.isMaster()) {
        const parentSettings = await new EntitySettingsService((entity as ChildEntity).getParent()).get();
        if (parentSettings.allowedStaticDomainsForChildId) {
            if (parentSettings.allowedStaticDomainsForChildId.indexOf(domain.id) === -1) {
                throw new Errors.ValidationError("Static domains restricted in parent entities");
            }
        }
    }
}

export function validateDomainTypeMatch(domain: StaticDomain, expectedType: StaticDomainType) {
    if (domain.type !== expectedType) {
        throw new Errors.ValidationError(
            `Domain type mismatch: Cannot assign ${domain.type} domain to ${expectedType} domain slot. ` +
            `Domain '${domain.domain}' has type '${domain.type}' but expected type '${expectedType}'.`
        );
    }
}
