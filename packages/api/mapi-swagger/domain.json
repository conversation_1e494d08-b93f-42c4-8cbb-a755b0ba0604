{"/domains/dynamic/{domainId}": {"parameters": [{"$ref": "#/parameters/domainId"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["domain", "domain:dynamic", "domain:dynamic:view"]}], "tags": ["Domain"], "summary": "Gets dynamic domain by id", "responses": {"200": {"description": "Domain found and returned", "schema": {"$ref": "#/definitions/Domain"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 900: Domain not found\n"}}}, "patch": {"security": [{"apiKey": []}, {"Permissions": ["domain", "domain:dynamic", "domain:dynamic:edit"]}], "tags": ["Domain"], "summary": "Updates dynamic domain by id", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/DynamicDomainData"}}], "responses": {"200": {"description": "Domain updated and returned", "schema": {"$ref": "#/definitions/Domain"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 900: Domain not found\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["domain", "domain:dynamic", "domain:dynamic:remove"]}], "tags": ["Domain"], "summary": "Deletes dynamic domain", "responses": {"204": {"description": "Successfully deleted domain"}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 900: Domain not found\n"}, "409": {"description": "- 901: Domain is used by entity\n"}}}}, "/domains/dynamic": {"post": {"security": [{"apiKey": []}, {"Permissions": ["domain", "domain:dynamic", "domain:dynamic:create"]}], "tags": ["Domain"], "summary": "Creates dynamic domain (game server domain)", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/DynamicDomainData"}}], "responses": {"201": {"description": "Created domain info", "schema": {"$ref": "#/definitions/Domain"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}, "get": {"parameters": [{"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"name": "environment", "in": "query", "description": "Environment name (exact match)", "required": false, "type": "string"}, {"name": "environment__contains", "in": "query", "description": "Environment name contains string", "required": false, "type": "string"}, {"name": "environment__in", "in": "query", "description": "List of environment names separated by commas", "required": false, "type": "string"}, {"$ref": "#/parameters/domainStrictEquality"}, {"$ref": "#/parameters/domainContains"}, {"$ref": "#/parameters/domainIn"}, {"name": "description", "in": "query", "description": "Description (exact match)", "required": false, "type": "string"}, {"name": "description__contains", "in": "query", "description": "Description contains string", "required": false, "type": "string"}, {"name": "description__in", "in": "query", "description": "List of descriptions separated by commas", "required": false, "type": "string"}, {"name": "expiryDate", "in": "query", "description": "Expiry date (exact match) in ISO 8601 format", "required": false, "type": "string", "format": "date-time"}, {"name": "expiryDate__gte", "in": "query", "description": "Expiry date greater than or equal to in ISO 8601 format", "required": false, "type": "string", "format": "date-time"}, {"name": "expiryDate__lte", "in": "query", "description": "Expiry date less than or equal to in ISO 8601 format", "required": false, "type": "string", "format": "date-time"}, {"name": "expiryDate__gt", "in": "query", "description": "Expiry date greater than in ISO 8601 format", "required": false, "type": "string", "format": "date-time"}, {"name": "expiryDate__lt", "in": "query", "description": "Expiry date less than in ISO 8601 format", "required": false, "type": "string", "format": "date-time"}, {"name": "status", "in": "query", "description": "Domain status", "required": false, "type": "string", "enum": ["active", "suspended"]}, {"name": "status__in", "in": "query", "description": "List of statuses separated by commas", "required": false, "type": "string"}, {"name": "status__ne", "in": "query", "description": "Status not equal to value", "required": false, "type": "string", "enum": ["active", "suspended"]}], "security": [{"apiKey": []}, {"Permissions": ["domain", "domain:dynamic", "domain:dynamic:view"]}], "tags": ["Domain"], "summary": "Gets list of dynamic domains with pagination support", "responses": {"200": {"description": "List of domains with pagination headers", "schema": {"type": "array", "items": {"$ref": "#/definitions/Domain"}}, "headers": {"x-paging-total": {"type": "integer", "description": "Total number of domains"}, "x-paging-limit": {"type": "integer", "description": "Number of domains per page"}, "x-paging-offset": {"type": "integer", "description": "Number of domains skipped"}}}, "400": {"description": "- 40: Validation error (invalid pagination parameters)\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}}, "/domains/static/{domainId}": {"parameters": [{"$ref": "#/parameters/domainId"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["domain", "domain:static", "domain:static:view"]}], "tags": ["Domain"], "summary": "Gets static domain by id", "responses": {"200": {"description": "Domain found and returned", "schema": {"$ref": "#/definitions/Domain"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 900: Domain not found\n"}}}, "patch": {"security": [{"apiKey": []}, {"Permissions": ["domain", "domain:static", "domain:static:edit"]}], "tags": ["Domain"], "summary": "Updates static domain by id", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/StaticDomainData"}}], "responses": {"200": {"description": "Domain updated and returned", "schema": {"$ref": "#/definitions/Domain"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 900: Domain not found\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["domain", "domain:static", "domain:static:remove"]}], "tags": ["Domain"], "summary": "Deletes static domain", "responses": {"204": {"description": "Successfully deleted domain"}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 900: Domain not found\n"}, "409": {"description": "- 901: Domain is used by entity\n"}}}}, "/domains/static": {"post": {"security": [{"apiKey": []}, {"Permissions": ["domain", "domain:static", "domain:static:create"]}], "tags": ["Domain"], "summary": "Creates static domain (game client domain)", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/StaticDomainData"}}], "responses": {"201": {"description": "Created domain info", "schema": {"$ref": "#/definitions/Domain"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}, "get": {"parameters": [{"$ref": "#/parameters/pathInQuery"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"name": "type", "in": "query", "description": "Static domain type", "required": false, "type": "string", "enum": ["static", "lobby", "live-streaming", "ehub"]}, {"name": "type__in", "in": "query", "description": "List of types separated by commas", "required": false, "type": "string"}, {"name": "type__ne", "in": "query", "description": "Type not equal to value", "required": false, "type": "string", "enum": ["static", "lobby", "live-streaming", "ehub"]}, {"$ref": "#/parameters/domainStrictEquality"}, {"$ref": "#/parameters/domainContains"}, {"$ref": "#/parameters/domainIn"}, {"name": "description", "in": "query", "description": "Description (exact match)", "required": false, "type": "string"}, {"name": "description__contains", "in": "query", "description": "Description contains string", "required": false, "type": "string"}, {"name": "description__in", "in": "query", "description": "List of descriptions separated by commas", "required": false, "type": "string"}, {"name": "expiryDate", "in": "query", "description": "Expiry date (exact match) in ISO 8601 format", "required": false, "type": "string", "format": "date-time"}, {"name": "expiryDate__gte", "in": "query", "description": "Expiry date greater than or equal to in ISO 8601 format", "required": false, "type": "string", "format": "date-time"}, {"name": "expiryDate__lte", "in": "query", "description": "Expiry date less than or equal to in ISO 8601 format", "required": false, "type": "string", "format": "date-time"}, {"name": "expiryDate__gt", "in": "query", "description": "Expiry date greater than in ISO 8601 format", "required": false, "type": "string", "format": "date-time"}, {"name": "expiryDate__lt", "in": "query", "description": "Expiry date less than in ISO 8601 format", "required": false, "type": "string", "format": "date-time"}, {"name": "status", "in": "query", "description": "Domain status", "required": false, "type": "string", "enum": ["active", "suspended"]}, {"name": "status__in", "in": "query", "description": "List of statuses separated by commas", "required": false, "type": "string"}, {"name": "status__ne", "in": "query", "description": "Status not equal to value", "required": false, "type": "string", "enum": ["active", "suspended"]}], "security": [{"apiKey": []}, {"Permissions": ["domain", "domain:static", "domain:static:view"]}], "tags": ["Domain"], "summary": "Gets list of static domains with pagination support", "responses": {"200": {"description": "List of domains with pagination headers", "schema": {"type": "array", "items": {"$ref": "#/definitions/StaticDomain"}}, "headers": {"x-paging-total": {"type": "integer", "description": "Total number of domains"}, "x-paging-limit": {"type": "integer", "description": "Number of domains per page"}, "x-paging-offset": {"type": "integer", "description": "Number of domains skipped"}}}, "400": {"description": "- 40: Validation error (invalid pagination parameters)\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}}, "/domains/lobby/{domainId}": {"parameters": [{"$ref": "#/parameters/domainId"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["domain", "domain:static", "domain:static:view"]}], "tags": ["Domain"], "summary": "Gets lobby domain by id", "responses": {"200": {"description": "Domain found and returned", "schema": {"$ref": "#/definitions/LobbyDomain"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 900: Domain not found\n"}}}, "patch": {"security": [{"apiKey": []}, {"Permissions": ["domain", "domain:static", "domain:static:edit"]}], "tags": ["Domain"], "summary": "Updates lobby domain by id", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/LobbyDomainData"}}], "responses": {"200": {"description": "Domain updated and returned", "schema": {"$ref": "#/definitions/LobbyDomain"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 900: Domain not found\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["domain", "domain:static", "domain:static:remove"]}], "tags": ["Domain"], "summary": "Deletes lobby domain", "responses": {"204": {"description": "Successfully deleted domain"}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 900: Domain not found\n"}, "409": {"description": "- 901: Domain is used by entity\n"}}}}, "/domains/lobby": {"post": {"security": [{"apiKey": []}, {"Permissions": ["domain", "domain:static", "domain:static:create"]}], "tags": ["Domain"], "summary": "Creates domain (lobby client domain)", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/LobbyDomainData"}}], "responses": {"201": {"description": "Created domain info", "schema": {"$ref": "#/definitions/LobbyDomain"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}, "get": {"security": [{"apiKey": []}, {"Permissions": ["domain", "domain:static", "domain:static:view"]}], "tags": ["Domain"], "summary": "Gets list of lobby domains", "responses": {"200": {"description": "List of domains", "schema": {"type": "array", "items": {"$ref": "#/definitions/LobbyDomain"}}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}}, "/{path}/entitydomain/dynamic/{domainId}": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/domainId"}], "put": {"security": [{"apiKey": []}, {"Permissions": ["entitydomain", "entitydomain:dynamic", "entitydomain:dynamic:edit"]}], "tags": ["Domain"], "summary": "Set entity dynamic domain. By default domains are inherited by children by path", "responses": {"200": {"description": "Domain found and set for entity", "schema": {"$ref": "#/definitions/Domain"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 771: Migration is in progress for one of the entity\n- 900: Domain not found\n"}}}}, "/{path}/entitydomain/dynamic": {"parameters": [{"$ref": "#/parameters/path"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["entitydomain", "entitydomain:dynamic", "entitydomain:dynamic:view"]}], "tags": ["Domain"], "summary": "Gets entity dynamic domain by path", "responses": {"200": {"description": "Domain found and returned", "schema": {"$ref": "#/definitions/Domain"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 900: Domain not found\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["entitydomain", "entitydomain:dynamic", "entitydomain:dynamic:remove"]}], "tags": ["Domain"], "summary": "Reset entity dynamic domain to parent value by path", "responses": {"200": {"description": "Domain updated and returned", "schema": {"$ref": "#/definitions/Domain"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 900: Domain not found\n"}}}}, "/entitydomain/dynamic/{domainId}": {"parameters": [{"$ref": "#/parameters/domainId"}], "put": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:entitydomain", "keyentity:entitydomain:dynamic", "keyentity:entitydomain:dynamic:edit"]}], "tags": ["Domain"], "summary": "Set key entity dynamic domain", "responses": {"200": {"description": "Domain found and set for entity", "schema": {"$ref": "#/definitions/Domain"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 771: Migration is in progress for one of the entity\n- 900: Domain not found\n"}}}}, "/entitydomain/dynamic": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:entitydomain", "keyentity:entitydomain:dynamic", "keyentity:entitydomain:dynamic:view"]}], "tags": ["Domain"], "summary": "Gets key entity dynamic domain", "responses": {"200": {"description": "Domain found and returned", "schema": {"$ref": "#/definitions/Domain"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 900: Domain not found\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:entitydomain", "keyentity:entitydomain:dynamic", "keyentity:entitydomain:dynamic:remove"]}], "tags": ["Domain"], "summary": "Reset key entity dynamic domain to parent value", "responses": {"200": {"description": "Domain updated and returned", "schema": {"$ref": "#/definitions/Domain"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 900: Domain not found\n"}}}}, "/{path}/entitydomain/static/tags": {"put": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/setDomainTags"}], "security": [{"apiKey": []}, {"Permissions": ["entitydomain", "entitydomain:static", "entitydomain:static:tags", "entitydomain:static:tags:set"]}], "tags": ["Domain"], "summary": "Set static domain tags by path", "responses": {"200": {"description": "Entity info", "schema": {"$ref": "#/definitions/EntityDomainInfo"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 51: Could not find entity\n"}}}, "delete": {"parameters": [{"$ref": "#/parameters/path"}], "security": [{"apiKey": []}, {"Permissions": ["entitydomain", "entitydomain:static", "entitydomain:static:tags", "entitydomain:static:tags:reset"]}], "tags": ["Domain"], "summary": "Reset static domain tags by path", "responses": {"200": {"description": "Entity info", "schema": {"$ref": "#/definitions/EntityDomainInfo"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/{path}/entitydomain/static/{domainId}": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/domainId"}], "put": {"security": [{"apiKey": []}, {"Permissions": ["entitydomain", "entitydomain:static", "entitydomain:static:edit"]}], "tags": ["Domain"], "summary": "Set entity static domain by path. By default domains are inherited by children.", "parameters": [{"in": "body", "name": "info", "required": false, "schema": {"type": "object", "properties": {"type": {"type": "string", "enum": ["static", "lobby", "live-streaming", "ehub"], "description": "Static domain type (defaults to 'static')", "default": "static"}}}}], "responses": {"200": {"description": "Domain found and set for entity", "schema": {"$ref": "#/definitions/Domain"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 900: Domain not found\n"}}}}, "/{path}/entitydomain/static": {"parameters": [{"$ref": "#/parameters/path"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["entitydomain", "entitydomain:static", "entitydomain:static:view"]}], "tags": ["Domain"], "summary": "Gets entity static domain by path", "parameters": [{"name": "type", "in": "query", "description": "Static domain type", "required": false, "type": "string", "enum": ["static", "lobby", "live-streaming", "ehub"], "default": "static"}], "responses": {"200": {"description": "Domain found and returned", "schema": {"$ref": "#/definitions/Domain"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 900: Domain not found\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["entitydomain", "entitydomain:static", "entitydomain:static:remove"]}], "tags": ["Domain"], "summary": "Reset entity static domain to parent value by path", "parameters": [{"in": "body", "name": "info", "required": false, "schema": {"type": "object", "properties": {"type": {"type": "string", "enum": ["static", "lobby", "live-streaming", "ehub"], "description": "Static domain type (defaults to 'static')", "default": "static"}}}}], "responses": {"200": {"description": "Domain updated and returned", "schema": {"$ref": "#/definitions/Domain"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 900: Domain not found\n"}}}}, "/entitydomain/static/{domainId}": {"parameters": [{"$ref": "#/parameters/domainId"}], "put": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:entitydomain", "keyentity:entitydomain:static", "keyentity:entitydomain:static:edit"]}], "tags": ["Domain"], "summary": "Set key entity static domain", "parameters": [{"in": "body", "name": "info", "required": false, "schema": {"type": "object", "properties": {"type": {"type": "string", "enum": ["static", "lobby", "live-streaming", "ehub"], "description": "Static domain type (defaults to 'static')", "default": "static"}}}}], "responses": {"200": {"description": "Domain found and set for entity", "schema": {"$ref": "#/definitions/Domain"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 900: Domain not found\n"}}}}, "/entitydomain/static": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:entitydomain", "keyentity:entitydomain:static", "keyentity:entitydomain:static:view"]}], "tags": ["Domain"], "summary": "Gets key entity static domain", "parameters": [{"name": "type", "in": "query", "description": "Static domain type", "required": false, "type": "string", "enum": ["static", "lobby", "live-streaming", "ehub"], "default": "static"}], "responses": {"200": {"description": "Domain found and returned", "schema": {"$ref": "#/definitions/Domain"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 900: Domain not found\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:entitydomain", "keyentity:entitydomain:static", "keyentity:entitydomain:static:remove"]}], "tags": ["Domain"], "summary": "Reset key entity static domain to parent value", "parameters": [{"in": "body", "name": "info", "required": false, "schema": {"type": "object", "properties": {"type": {"type": "string", "enum": ["static", "lobby", "live-streaming", "ehub"], "description": "Static domain type (defaults to 'static')", "default": "static"}}}}], "responses": {"200": {"description": "Domain updated and returned", "schema": {"$ref": "#/definitions/Domain"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 900: Domain not found\n"}}}}}